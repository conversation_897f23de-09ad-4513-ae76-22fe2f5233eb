/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@300;400;500;600;700;800;900&display=swap');
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Barlow, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
html {
    font-family: 'Barlow', sans-serif;
  }
body{
  --tw-bg-opacity: 1;
  background-color: rgb(240 235 222 / var(--tw-bg-opacity, 1));
  font-family: Barlow, sans-serif;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
h1, h2, h3, h4, h5, h6{
  font-family: Barlow, sans-serif;
  font-weight: 700;
}
.btn-primary{
  border-radius: 1rem;
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
  background-color: transparent;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
.btn-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(240 235 222 / var(--tw-text-opacity, 1));
}
.btn-red{
  border-radius: 1rem;
  border-width: 2px;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  --tw-border-opacity: 1;
  border-color: rgb(233 68 54 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(233 68 54 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(240 235 222 / var(--tw-text-opacity, 1));
}
.btn-red:hover{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(233 68 54 / var(--tw-text-opacity, 1));
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.inset-0{
  inset: 0px;
}
.-bottom-2{
  bottom: -0.5rem;
}
.-bottom-4{
  bottom: -1rem;
}
.-bottom-6{
  bottom: -1.5rem;
}
.-left-4{
  left: -1rem;
}
.-left-6{
  left: -1.5rem;
}
.-right-12{
  right: -3rem;
}
.-right-16{
  right: -4rem;
}
.-right-2{
  right: -0.5rem;
}
.-right-4{
  right: -1rem;
}
.-right-6{
  right: -1.5rem;
}
.-right-8{
  right: -2rem;
}
.-top-4{
  top: -1rem;
}
.-top-6{
  top: -1.5rem;
}
.-top-8{
  top: -2rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1\/3{
  bottom: 33.333333%;
}
.bottom-1\/4{
  bottom: 25%;
}
.bottom-12{
  bottom: 3rem;
}
.bottom-16{
  bottom: 4rem;
}
.bottom-20{
  bottom: 5rem;
}
.bottom-24{
  bottom: 6rem;
}
.bottom-28{
  bottom: 7rem;
}
.bottom-32{
  bottom: 8rem;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-8{
  bottom: 2rem;
}
.left-0{
  left: 0px;
}
.left-1\/2{
  left: 50%;
}
.left-1\/3{
  left: 33.333333%;
}
.left-1\/4{
  left: 25%;
}
.left-12{
  left: 3rem;
}
.left-16{
  left: 4rem;
}
.left-20{
  left: 5rem;
}
.left-24{
  left: 6rem;
}
.left-3{
  left: 0.75rem;
}
.left-3\/4{
  left: 75%;
}
.left-32{
  left: 8rem;
}
.left-4{
  left: 1rem;
}
.left-8{
  left: 2rem;
}
.right-0{
  right: 0px;
}
.right-1\/3{
  right: 33.333333%;
}
.right-1\/4{
  right: 25%;
}
.right-12{
  right: 3rem;
}
.right-16{
  right: 4rem;
}
.right-20{
  right: 5rem;
}
.right-32{
  right: 8rem;
}
.right-4{
  right: 1rem;
}
.right-8{
  right: 2rem;
}
.top-0{
  top: 0px;
}
.top-1\/2{
  top: 50%;
}
.top-1\/3{
  top: 33.333333%;
}
.top-1\/4{
  top: 25%;
}
.top-12{
  top: 3rem;
}
.top-16{
  top: 4rem;
}
.top-20{
  top: 5rem;
}
.top-3{
  top: 0.75rem;
}
.top-3\/4{
  top: 75%;
}
.top-32{
  top: 8rem;
}
.top-4{
  top: 1rem;
}
.top-48{
  top: 12rem;
}
.top-6{
  top: 1.5rem;
}
.top-8{
  top: 2rem;
}
.-left-8{
  left: -2rem;
}
.bottom-1\/2{
  bottom: 50%;
}
.left-full{
  left: 100%;
}
.right-full{
  right: 100%;
}
.-bottom-8{
  bottom: -2rem;
}
.left-10{
  left: 2.5rem;
}
.top-40{
  top: 10rem;
}
.z-10{
  z-index: 10;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.-mt-\[15px\]{
  margin-top: -15px;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-8{
  margin-left: 2rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mb-20{
  margin-bottom: 5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mt-20{
  margin-top: 5rem;
}
.mt-16{
  margin-top: 4rem;
}
.mr-4{
  margin-right: 1rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.flex{
  display: flex;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.h-0{
  height: 0px;
}
.h-1{
  height: 0.25rem;
}
.h-1\/2{
  height: 50%;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-28{
  height: 7rem;
}
.h-3{
  height: 0.75rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-48{
  height: 12rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-8{
  height: 2rem;
}
.h-96{
  height: 24rem;
}
.h-\[600px\]{
  height: 600px;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-0\.5{
  height: 0.125rem;
}
.h-7{
  height: 1.75rem;
}
.h-80{
  height: 20rem;
}
.h-9{
  height: 2.25rem;
}
.h-40{
  height: 10rem;
}
.min-h-\[300px\]{
  min-height: 300px;
}
.min-h-screen{
  min-height: 100vh;
}
.w-0{
  width: 0px;
}
.w-1\/2{
  width: 50%;
}
.w-10{
  width: 2.5rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-18{
  width: 4.5rem;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-28{
  width: 7rem;
}
.w-3{
  width: 0.75rem;
}
.w-32{
  width: 8rem;
}
.w-4{
  width: 1rem;
}
.w-40{
  width: 10rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-8{
  width: 2rem;
}
.w-auto{
  width: auto;
}
.w-full{
  width: 100%;
}
.w-px{
  width: 1px;
}
.w-2{
  width: 0.5rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-6xl{
  max-width: 72rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-45{
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-12{
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.resize-none{
  resize: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.flex-col{
  flex-direction: column;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-12{
  gap: 3rem;
}
.gap-16{
  gap: 4rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-24 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(6rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(6rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.overflow-hidden{
  overflow: hidden;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-sm{
  border-radius: 0.125rem;
}
.rounded-b-full{
  border-bottom-right-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.rounded-l-full{
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.rounded-r-full{
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}
.rounded-t-full{
  border-top-left-radius: 9999px;
  border-top-right-radius: 9999px;
}
.rounded-bl-full{
  border-bottom-left-radius: 9999px;
}
.rounded-br-full{
  border-bottom-right-radius: 9999px;
}
.rounded-tl-full{
  border-top-left-radius: 9999px;
}
.rounded-tr-full{
  border-top-right-radius: 9999px;
}
.border{
  border-width: 1px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-8{
  border-width: 8px;
}
.border-b-\[28px\]{
  border-bottom-width: 28px;
}
.border-b-\[42px\]{
  border-bottom-width: 42px;
}
.border-b-\[56px\]{
  border-bottom-width: 56px;
}
.border-b-\[84px\]{
  border-bottom-width: 84px;
}
.border-l-\[16px\]{
  border-left-width: 16px;
}
.border-l-\[24px\]{
  border-left-width: 24px;
}
.border-l-\[32px\]{
  border-left-width: 32px;
}
.border-l-\[48px\]{
  border-left-width: 48px;
}
.border-r-\[16px\]{
  border-right-width: 16px;
}
.border-r-\[24px\]{
  border-right-width: 24px;
}
.border-r-\[32px\]{
  border-right-width: 32px;
}
.border-r-\[48px\]{
  border-right-width: 48px;
}
.border-t{
  border-top-width: 1px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-8{
  border-bottom-width: 8px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r-4{
  border-right-width: 4px;
}
.border-bauhaus-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-bauhaus-blue{
  --tw-border-opacity: 1;
  border-color: rgb(67 72 151 / var(--tw-border-opacity, 1));
}
.border-bauhaus-yellow{
  --tw-border-opacity: 1;
  border-color: rgb(255 197 39 / var(--tw-border-opacity, 1));
}
.border-black{
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-brand-background{
  --tw-border-opacity: 1;
  border-color: rgb(240 235 222 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-bauhaus-red{
  --tw-border-opacity: 1;
  border-color: rgb(233 68 54 / var(--tw-border-opacity, 1));
}
.border-b-transparent{
  border-bottom-color: transparent;
}
.border-l-transparent{
  border-left-color: transparent;
}
.border-r-transparent{
  border-right-color: transparent;
}
.border-t-transparent{
  border-top-color: transparent;
}
.border-b-bauhaus-black{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.bg-bauhaus-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-bauhaus-blue{
  --tw-bg-opacity: 1;
  background-color: rgb(67 72 151 / var(--tw-bg-opacity, 1));
}
.bg-bauhaus-red{
  --tw-bg-opacity: 1;
  background-color: rgb(233 68 54 / var(--tw-bg-opacity, 1));
}
.bg-bauhaus-white{
  --tw-bg-opacity: 1;
  background-color: rgb(240 235 222 / var(--tw-bg-opacity, 1));
}
.bg-bauhaus-yellow{
  --tw-bg-opacity: 1;
  background-color: rgb(255 197 39 / var(--tw-bg-opacity, 1));
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-brand-background{
  --tw-bg-opacity: 1;
  background-color: rgb(240 235 222 / var(--tw-bg-opacity, 1));
}
.bg-brand-blue{
  --tw-bg-opacity: 1;
  background-color: rgb(67 72 151 / var(--tw-bg-opacity, 1));
}
.bg-brand-red{
  --tw-bg-opacity: 1;
  background-color: rgb(233 68 54 / var(--tw-bg-opacity, 1));
}
.bg-brand-yellow{
  --tw-bg-opacity: 1;
  background-color: rgb(255 197 39 / var(--tw-bg-opacity, 1));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-24{
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.pb-16{
  padding-bottom: 4rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pt-16{
  padding-top: 4rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-8{
  padding-top: 2rem;
}
.pt-12{
  padding-top: 3rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-body{
  font-size: 1rem;
  line-height: 1.6;
}
.text-display{
  font-size: 3rem;
  line-height: 1.2;
  letter-spacing: -0.01em;
}
.text-heading{
  font-size: 2rem;
  line-height: 1.3;
}
.text-hero{
  font-size: 4rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-8xl{
  font-size: 6rem;
  line-height: 1;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-5xl{
  font-size: 3rem;
  line-height: 1;
}
.text-6xl{
  font-size: 3.75rem;
  line-height: 1;
}
.font-bold{
  font-weight: 700;
}
.font-medium{
  font-weight: 500;
}
.uppercase{
  text-transform: uppercase;
}
.not-italic{
  font-style: normal;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.leading-none{
  line-height: 1;
}
.tracking-wide{
  letter-spacing: 0.1em;
}
.text-bauhaus-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-bauhaus-blue{
  --tw-text-opacity: 1;
  color: rgb(67 72 151 / var(--tw-text-opacity, 1));
}
.text-bauhaus-red{
  --tw-text-opacity: 1;
  color: rgb(233 68 54 / var(--tw-text-opacity, 1));
}
.text-bauhaus-white{
  --tw-text-opacity: 1;
  color: rgb(240 235 222 / var(--tw-text-opacity, 1));
}
.text-bauhaus-yellow{
  --tw-text-opacity: 1;
  color: rgb(255 197 39 / var(--tw-text-opacity, 1));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-brand-background{
  --tw-text-opacity: 1;
  color: rgb(240 235 222 / var(--tw-text-opacity, 1));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-red-100{
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.opacity-10{
  opacity: 0.1;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-25{
  opacity: 0.25;
}
.opacity-30{
  opacity: 0.3;
}
.opacity-40{
  opacity: 0.4;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-80{
  opacity: 0.8;
}
.opacity-85{
  opacity: 0.85;
}
.opacity-90{
  opacity: 0.9;
}
.opacity-15{
  opacity: 0.15;
}
.opacity-5{
  opacity: 0.05;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}

.hover\:bg-transparent:hover{
  background-color: transparent;
}

.hover\:text-bauhaus-red:hover{
  --tw-text-opacity: 1;
  color: rgb(233 68 54 / var(--tw-text-opacity, 1));
}

.hover\:text-brand-background:hover{
  --tw-text-opacity: 1;
  color: rgb(240 235 222 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover{
  text-decoration-line: underline;
}

.focus\:border-bauhaus-red:focus{
  --tw-border-opacity: 1;
  border-color: rgb(233 68 54 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}

@media (min-width: 768px){
  .md\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .md\:col-span-5{
    grid-column: span 5 / span 5;
  }
  .md\:col-span-7{
    grid-column: span 7 / span 7;
  }
  .md\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .md\:block{
    display: block;
  }
  .md\:flex{
    display: flex;
  }
  .md\:hidden{
    display: none;
  }
  .md\:h-16{
    height: 4rem;
  }
  .md\:h-\[700px\]{
    height: 700px;
  }
  .md\:w-8{
    width: 2rem;
  }
  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:flex-row{
    flex-direction: row;
  }
  .md\:items-center{
    align-items: center;
  }
  .md\:gap-12{
    gap: 3rem;
  }
  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .md\:p-12{
    padding: 3rem;
  }
  .md\:p-8{
    padding: 2rem;
  }
  .md\:px-12{
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .md\:py-24{
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
  .md\:py-32{
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
  .md\:pb-24{
    padding-bottom: 6rem;
  }
  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }
  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .md\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }
  .md\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }
  .md\:text-9xl{
    font-size: 8rem;
    line-height: 1;
  }
  .md\:text-7xl{
    font-size: 4.5rem;
    line-height: 1;
  }
}

@media (min-width: 1024px){
  .lg\:order-1{
    order: 1;
  }
  .lg\:order-2{
    order: 2;
  }
  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .lg\:col-span-8{
    grid-column: span 8 / span 8;
  }
  .lg\:block{
    display: block;
  }
  .lg\:h-24{
    height: 6rem;
  }
  .lg\:h-\[500px\]{
    height: 500px;
  }
  .lg\:h-\[600px\]{
    height: 600px;
  }
  .lg\:w-12{
    width: 3rem;
  }
  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:justify-start{
    justify-content: flex-start;
  }
  .lg\:justify-end{
    justify-content: flex-end;
  }
  .lg\:gap-16{
    gap: 4rem;
  }
  .lg\:px-24{
    padding-left: 6rem;
    padding-right: 6rem;
  }
  .lg\:text-left{
    text-align: left;
  }
  .lg\:text-right{
    text-align: right;
  }
  .lg\:text-7xl{
    font-size: 4.5rem;
    line-height: 1;
  }
}

@media (min-width: 1280px){
  .xl\:h-32{
    height: 8rem;
  }
  .xl\:w-16{
    width: 4rem;
  }
}

